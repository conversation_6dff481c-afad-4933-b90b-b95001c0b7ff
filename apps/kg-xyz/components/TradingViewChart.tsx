'use client';

import {
  type <PERSON><PERSON><PERSON>,
  create<PERSON><PERSON>,
  CrosshairMode,
  type HistogramData,
  type IChartApi,
  type IPriceLine,
  type ISeriesApi,
  LineStyle,
  PriceScaleMode,
  type SeriesMarker,
  type Time,
  type UTCTimestamp,
} from 'lightweight-charts';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { usePrices } from '@/contexts/price-context';
import { useActiveLimitOrdersQuery } from '@/hooks/api-queries';
import { useDebugWallet } from '@/hooks/use-debug-wallet';
import { useBuySignals, useSellSignals } from '@/hooks/use-signals';
import { useTransactions } from '@/hooks/use-transactions';
import { SOL_ADDRESS } from '@/lib/chains';
import { formatJupiterOrders, formatNumber, formatPriceWithDecimals } from '@/lib/format';
import type { OKXCandleResult, OKXTransaction } from '@/types/okx';
import { useConnection } from '@solana/wallet-adapter-react';
// These imports exist but TypeScript might not recognize them correctly
import { PublicKey } from '@solana/web3.js';

import { sleep } from '@/lib/utils';
import { QuotationWebSocket } from './QuotationWebSocket';

// Extend the time frames to include 1D
type TimeFrame = '1m' | '5m' | '15m' | '1H' | '4H' | '1D';

interface TradingViewChartProps {
  exchange?: string;
  tokenAddress: string;
  chainId: string;
  inApp?: boolean;
}

interface ChartData {
  time: Time;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

// Include 1D in the available timeframes
const timeFrames: TimeFrame[] = ['1m', '5m', '15m', '1H', '4H', '1D'];
const timeFrameSeconds: Record<TimeFrame, number> = {
  '1m': 60,
  '5m': 300,
  '15m': 900,
  '1H': 3600,
  '4H': 14400,
  '1D': 86400,
};

const fetchChartData = async (
  tokenAddress: string,
  chainId: string,
  timeFrame: TimeFrame,
  before?: number,
  limit = 1000,
): Promise<ChartData[]> => {
  try {
    const now = Date.now();
    const after = before ? before : now;
    const url =
      `/api/okx/candles?chainId=${chainId}&address=${tokenAddress}&after=${after}&bar=${timeFrame}&limit=${limit}`;
    const response = await fetch(url, {
      headers: { accept: 'application/json' },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = (await response.json()) as OKXCandleResult;
    if (!result || result.length === 0) return [];

    const chartData = result
      .map((candle) => ({
        time: Math.floor(parseInt(candle[0]) / 1000) as UTCTimestamp,
        open: parseFloat(candle[1]),
        high: parseFloat(candle[2]),
        low: parseFloat(candle[3]),
        close: parseFloat(candle[4]),
        volume: parseFloat(candle[5]),
      }))
      .sort((a, b) => (a.time as number) - (b.time as number));

    return chartData;
  } catch (error) {
    console.error('Failed to fetch chart data:', error);
    return [];
  }
};

export function TradingViewChart({ tokenAddress, chainId, inApp }: TradingViewChartProps) {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null);
  const volumeSeriesRef = useRef<ISeriesApi<'Histogram'> | null>(null);
  const balanceSeriesRef = useRef<ISeriesApi<'Line'> | null>(null);
  const solBalanceSeriesRef = useRef<ISeriesApi<'Line'> | null>(null);
  const lastBarRef = useRef<ChartData | null>(null);
  const priceLineRefs = useRef<Map<string, IPriceLine>>(new Map());

  const [selectedTimeFrame, setSelectedTimeFrame] = useState<TimeFrame>('5m');
  const { prices, registerToken, updateTokenPrice } = usePrices();
  const historicalDataRef = useRef<ChartData[]>([]);
  const isLoadingMoreDataRef = useRef(false);
  const oldestCandleTimeRef = useRef<number | null>(null);
  const { address: publicKey } = useDebugWallet();
  const solPrice = prices[`sol:${SOL_ADDRESS}`] || 0;
  const { connection } = useConnection();
  const [tokenDecimals, setTokenDecimals] = useState(6);
  const [suspectWalletBalanceHistory, setSuspectWalletBalanceHistory] = useState([{
    timestamp: 0,
    balance: 0,
    sol_balance: 0,
  }]);

  useEffect(() => {
    connection.getAccountInfo(new PublicKey(tokenAddress)).then((accountInfo) => {
      if (accountInfo && accountInfo.data) {
        const array = Array.from(accountInfo.data);
        const decimals = array[44] || 0;
        setTokenDecimals(decimals);
      }
    });

    import(`./${tokenAddress}.json`).then((data) => {
      setSuspectWalletBalanceHistory(
        Array.from(data).filter(x => x.timestamp).toSorted((a, b) => a.timestamp - b.timestamp),
      );
    }).catch(() =>
      fetch('http://localhost:8088/analyze/suspect_wallets_balances_enhanced', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: tokenAddress,
          from_slot: *********,
          extra_wallets: [
            '7sMiW38oLg5q9SKNoyPaH2Ee1VhpGdMCDrC4Lo4uLBBE',
            '4FDKx3S3k9eD7HeAhjQxHeYNLXHtreCD1GTUWktiYUvR',
            '5sTQ5ih7xtctBhMXHr3f1aWdaXazWrWfoehqWdqWnTFP',
            'MfDuWeqSHEqTFVYZ7LoexgAK9dxk7cy4DFJWjWMGVWa',
            '44P5Ct5JkPz76Rs2K6juC65zXMpFRDrHatxcASJ4Dyra',
          ],
        }),
      })
        .then((res) => res.json())
        .then((data) => {
          setSuspectWalletBalanceHistory(data.data.balance_history.balance_points.filter(point => point.timestamp));
        })
    ).catch((error) => {
      console.error('Failed to fetch suspect wallet balance history:', error);
      setSuspectWalletBalanceHistory([{ timestamp: 0, balance: 0, sol_balance: 0 }]);
    });
  }, [connection, tokenAddress]);

  const { transactions } = useTransactions({
    tokenAddress,
    refetchInterval: 3000,
  });

  const { data: buySignals } = useBuySignals();
  const { data: sellSignals } = useSellSignals();

  const buySignalTime = buySignals?.find((signal) => signal.token_address === tokenAddress)?.emit_time
    ?? sellSignals?.find((signal) => signal.token_address === tokenAddress)?.buy_entry_time;

  // Process balance data for chart display
  const processedBalanceData = useMemo(() => {
    return suspectWalletBalanceHistory.map(point => ({
      time: point.timestamp as UTCTimestamp,
      value: point.balance / 1e6,
    }));
  }, [suspectWalletBalanceHistory]);

  const processedSolBalanceData = useMemo(() => {
    return suspectWalletBalanceHistory.map(point => ({
      time: point.timestamp as UTCTimestamp,
      value: point.sol_balance / 1e9,
    }));
  }, [suspectWalletBalanceHistory]);

  // State to track tooltip content
  const [tooltipContent, setTooltipContent] = useState({
    time: '',
    open: 0,
    high: 0,
    low: 0,
    close: 0,
    volume: 0,
    visible: false,
  });

  // Use the centralized active limit orders query
  const { data: limitOrders } = useActiveLimitOrdersQuery({
    variables: { publicKey: publicKey || '' },
    enabled: !!publicKey,
    refetchInterval: 5000,
  });

  // Map the API response to our LimitOrder interface
  const orders = useMemo(() => {
    if (!limitOrders?.orders?.length) return [];
    return formatJupiterOrders(limitOrders.orders, tokenAddress, tokenDecimals, solPrice);
  }, [limitOrders?.orders, tokenAddress, tokenDecimals, solPrice]);

  // Register the token address with the PriceProvider
  useEffect(() => {
    registerToken(tokenAddress);
  }, [tokenAddress, registerToken]);

  // Handle real-time data updates
  const handleRealtimeData = useCallback(
    async (wsData: any) => {
      if (!wsData.data?.[0] || !candlestickSeriesRef.current || !volumeSeriesRef.current) return;

      const klineData = wsData?.data?.[0];
      const newBar: ChartData = {
        time: Math.floor(klineData.t) as UTCTimestamp,
        open: parseFloat(klineData.o),
        high: parseFloat(klineData.h),
        low: parseFloat(klineData.l),
        close: parseFloat(klineData.c),
        volume: parseFloat(klineData.v),
      };

      // Update price in context
      updateTokenPrice(tokenAddress, newBar.close);

      // Update HTML title with new price
      const formattedPrice = newBar.close.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 6,
      });
      const symbol = document.title.split(' ')[0];
      document.title = `${symbol} ${formattedPrice} Price, Chart & Info | KryptoGO`;

      // If this is a new bar
      if (!lastBarRef.current || lastBarRef.current.time !== newBar.time) {
        // If we have a last bar, update it one final time before adding new
        if (lastBarRef.current) {
          candlestickSeriesRef.current.update(lastBarRef.current);
          volumeSeriesRef.current.update({
            time: lastBarRef.current.time,
            value: lastBarRef.current.volume,
            color: lastBarRef.current.close >= lastBarRef.current.open ? '#00b38c' : '#db2777',
          });

          // Set the new bar's open price to the last bar's close price
          newBar.open = lastBarRef.current.close;
          // Adjust high/low if needed based on the new open price
          newBar.high = Math.max(newBar.high, newBar.open);
          newBar.low = Math.min(newBar.low, newBar.open);
        }

        // Add the new bar
        candlestickSeriesRef.current.update(newBar);
        volumeSeriesRef.current.update({
          time: newBar.time,
          value: newBar.volume,
          color: newBar.close >= newBar.open ? '#00B38C' : '#db2777', // Tailwind pink-600
        });

        // Update last bar reference
        lastBarRef.current = newBar;

        // Update historical data by appending the new bar
        historicalDataRef.current = [...historicalDataRef.current, newBar];
      } else {
        if (lastBarRef.current) {
          newBar.open = lastBarRef.current.open;
          newBar.high = Math.max(newBar.high, newBar.open);
          newBar.low = Math.min(newBar.low, newBar.open);
        }
        // Update the current bar
        candlestickSeriesRef.current.update(newBar);
        volumeSeriesRef.current.update({
          time: newBar.time,
          value: newBar.volume,
          color: newBar.close >= newBar.open ? '#00B38C' : '#db2777', // Tailwind pink-600
        });

        // Update the last bar in historical data
        const newData = [...historicalDataRef.current];
        // Keep the original open price when updating existing bar
        newBar.open = newData[newData.length - 1].open;
        // Adjust high/low based on all prices in this bar
        newBar.high = Math.max(newBar.high, newBar.open, newBar.close);
        newBar.low = Math.min(newBar.low, newBar.open, newBar.close);
        newData[newData.length - 1] = newBar;
        historicalDataRef.current = newData;

        lastBarRef.current = newBar;
      }
    },
    [tokenAddress, updateTokenPrice],
  );

  // Format transactions as chart markers
  const formatTransactionMarkers = (transactions: OKXTransaction[]): SeriesMarker<Time>[] => {
    return transactions.map((tx) => {
      // Convert milliseconds to seconds for UTCTimestamp
      // not sure why need to subtract, but this will become correct position
      const time = (Math.floor(tx.blockTime / 1000) - timeFrameSeconds[selectedTimeFrame]) as UTCTimestamp;
      return {
        time,
        position: tx.type === 1 ? 'belowBar' : 'aboveBar',
        color: tx.type === 1 ? '#00B38C' : '#db2777', // Green for Buy, Red for Sell (matching the image)
        shape: 'circle',
        text: tx.type === 1 ? 'B' : 'S',
        size: 2, // Increase size to make it more visible
        id: `tx-${tx.id}`,
      };
    });
  };

  // Add horizontal price lines for limit orders
  useEffect(() => {
    if (!candlestickSeriesRef.current || !orders || orders.length === 0) return;

    // Clear previous price lines
    priceLineRefs.current.forEach((line) => {
      candlestickSeriesRef.current?.removePriceLine(line);
    });
    priceLineRefs.current.clear();

    // Add new price lines for each limit order
    orders.forEach((order) => {
      if (!candlestickSeriesRef.current) return;

      const isBuy = order.type === 'buy';
      try {
        const priceLine = candlestickSeriesRef.current.createPriceLine({
          price: order.price,
          color: isBuy ? '#00B38C' : '#db2777', // Tailwind pink-600
          lineWidth: 1,
          lineStyle: LineStyle.Dashed,
          axisLabelVisible: true,
          title: isBuy
            ? `BUY @ ${formatPriceWithDecimals(order.price)}`
            : `SELL @ ${formatPriceWithDecimals(order.price)}`,
        });

        priceLineRefs.current.set(order.orderKey, priceLine);
      } catch (error) {
        console.error('Error creating price line for order:', order.orderKey, error);
      }
    });

    const priceLineCurrentRefs = priceLineRefs.current;
    return () => {
      // Cleanup: remove all price lines when component unmounts
      if (candlestickSeriesRef.current) {
        priceLineCurrentRefs.forEach((line) => {
          candlestickSeriesRef.current?.removePriceLine(line);
        });
        priceLineCurrentRefs.clear();
      }
    };
  }, [orders, tokenAddress, solPrice]);

  // Memoize the QuotationWebSocket props
  const quotationWebSocketProps = useMemo(
    () => ({
      chainId: chainId == '501' ? 'sol' : 'eth',
      tokenAddress,
      timeFrame: selectedTimeFrame,
      onData: handleRealtimeData,
    }),
    [chainId, tokenAddress, selectedTimeFrame, handleRealtimeData],
  );

  // Function to load more historical data when user scrolls to the left
  const loadMoreHistoricalData = async () => {
    if (isLoadingMoreDataRef.current) return;
    isLoadingMoreDataRef.current = true;
    console.log('loadMoreHistoricalData called', {
      tokenAddress,
      chainId,
      oldestCandleTime: oldestCandleTimeRef.current,
      isLoadingMoreDataRef,
    });

    if (!tokenAddress || !chainId || !oldestCandleTimeRef.current) {
      return;
    }

    try {
      // Convert oldest candle time from UTCTimestamp (seconds) to milliseconds for the API
      const oldestTimeMs = oldestCandleTimeRef.current * 1000;

      // Call our custom API endpoint to fetch historical data
      const url =
        `/api/okx-dex/token/market/history-dex-token-hlc-candles?chainId=${chainId}&address=${tokenAddress}&after=${oldestTimeMs}&bar=${selectedTimeFrame}&limit=300&t=${Date.now().toString()}`;

      const response = await fetch(url, {
        headers: { accept: 'application/json', 'x-request-timestamp': Date.now().toString() },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const olderData = (await response.json()).data as OKXCandleResult;
      console.log(`Received ${olderData.length || 0} historical candles`);

      if (olderData && olderData.length > 0) {
        // Format the data for the chart
        const formattedOlderData = olderData
          .map((candle: string[]) => ({
            time: Math.floor(parseInt(candle[0]) / 1000) as UTCTimestamp,
            open: parseFloat(candle[1]),
            high: parseFloat(candle[2]),
            low: parseFloat(candle[3]),
            close: parseFloat(candle[4]),
            volume: parseFloat(candle[5]),
          }))
          .sort((a: ChartData, b: ChartData) => (a.time as number) - (b.time as number));

        // Update the oldest candle time
        if (formattedOlderData.length > 0) {
          const newOldestTime = formattedOlderData[0].time as number;
          console.log('New oldest candle time:', new Date(newOldestTime * 1000).toISOString());
          // setOldestCandleTime(newOldestTime);
        }

        // Merge with existing data, avoiding duplicates, and update the chart
        // Filter out any duplicates from the new data
        const uniqueNewData = formattedOlderData;
        // Combine and sort the data
        const combinedData = [...uniqueNewData, ...historicalDataRef.current].sort(
          (a: ChartData, b: ChartData) => (a.time as number) - (b.time as number),
        );

        console.log(`Updating chart with ${combinedData.length} candles (${uniqueNewData.length} new)`);

        // Update the ref with combined data
        historicalDataRef.current = combinedData;

        // Update the chart with the combined data
        if (candlestickSeriesRef.current && volumeSeriesRef.current && chartRef.current) {
          try {
            // First update candlestick series
            candlestickSeriesRef.current.setData(combinedData);

            // Then update volume series
            const volumeData = combinedData.map((d: ChartData) => ({
              time: d.time,
              value: d.volume,
              color: d.close >= d.open ? '#00B38C' : '#db2777',
            }));
            volumeSeriesRef.current.setData(volumeData);
            oldestCandleTimeRef.current = +combinedData[0].time;
          } catch (error) {
            console.error('Error updating chart data:', error);
          }
        }
      } else {
        console.log('No historical data received');
      }
    } catch (error) {
      console.error('Failed to fetch historical data:', error);
    } finally {
      isLoadingMoreDataRef.current = false;
    }
  };

  // Add transaction markers to chart when historical data is loaded
  useEffect(() => {
    if (!candlestickSeriesRef.current || historicalDataRef.current.length === 0) return;

    // Create markers from transaction data
    console.log(
      'txs',
      transactions?.toSorted((a, b) => a.blockTime - b.blockTime),
    );
    const markers = formatTransactionMarkers(transactions?.toSorted((a, b) => a.blockTime - b.blockTime) || []);
    if (buySignalTime) {
      const signalMarker: SeriesMarker<Time> = {
        time: buySignalTime as UTCTimestamp,
        position: 'belowBar',
        color: '#FFC211',
        shape: 'arrowUp',
        text: '💎',
        size: 2,
        id: 'buy-signal',
      };
      markers.push(signalMarker);
      markers.sort((a, b) => (a.time as number) - (b.time as number));
    }

    // Add markers to the chart
    candlestickSeriesRef.current!.setMarkers(markers);
  }, [historicalDataRef.current.length, transactions, buySignalTime, candlestickSeriesRef.current]);

  // Initialize chart with time range handler
  useEffect(() => {
    if (!chartContainerRef.current || !tokenAddress || !chainId) return;

    const handleResize = () => {
      if (chartRef.current && chartContainerRef.current) {
        chartRef.current.applyOptions({
          width: chartContainerRef.current.clientWidth,
          height: chartContainerRef.current.clientHeight,
        });
      }
    };

    // Create a ResizeObserver to monitor container size changes
    const resizeObserver = new ResizeObserver(() => {
      handleResize();
    });

    // Start observing the container
    resizeObserver.observe(chartContainerRef.current);

    const chart = createChart(chartContainerRef.current, {
      localization: {
        timeFormatter: (time: number) => {
          const date = new Date(time * 1000);
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          const hour = String(date.getHours()).padStart(2, '0');
          const minute = String(date.getMinutes()).padStart(2, '0');
          return `${year}/${month}/${day} ${hour}:${minute}`;
        },
      },
      layout: {
        background: { color: '#09090B' },
        textColor: '#A1A1AA',
      },
      grid: {
        vertLines: { color: '#1B1B1B' },
        horzLines: { color: '#1B1B1B' },
      },
      rightPriceScale: {
        borderColor: '#1B1B1B',
        mode: PriceScaleMode.Percentage,
        autoScale: true,
        invertScale: false,
        alignLabels: true,
        borderVisible: true,
        scaleMargins: {
          top: 0.1,
          bottom: 0.25,
        },
        ticksVisible: true,
      },
      crosshair: {
        mode: CrosshairMode.Magnet,
        vertLine: {
          width: 1,
          color: '#C3BCDB44',
          style: LineStyle.Solid,
          labelVisible: true,
        },
        horzLine: {
          width: 1,
          color: '#C3BCDB44',
          style: LineStyle.Solid,
          labelVisible: true,
        },
      },
      timeScale: {
        borderColor: '#1B1B1B',
        timeVisible: true,
        secondsVisible: false,
        barSpacing: 8,
        rightOffset: 0,
        borderVisible: true,
        fixRightEdge: false,
        ticksVisible: true,
        // Format tick marks on the time axis
        tickMarkFormatter: (time: number) => {
          const date = new Date(time * 1000);
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          const hour = String(date.getHours()).padStart(2, '0');
          const minute = String(date.getMinutes()).padStart(2, '0');
          return `${month}/${day} ${hour}:${minute}`;
        },
      },
      handleScroll: {
        mouseWheel: true,
        pressedMouseMove: true,
      },
      handleScale: {
        axisPressedMouseMove: true,
        pinch: true,
      },
      width: chartContainerRef.current.clientWidth,
      height: chartContainerRef.current.clientHeight,
    });

    // Candlestick series
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#00B38C',
      downColor: '#db2777', // Tailwind pink-600
      borderVisible: false,
      wickUpColor: '#00B38C',
      wickDownColor: '#db2777', // Tailwind pink-600
      priceFormat: {
        type: 'custom',
        formatter: (price: number) => {
          if (price === 0) return '0';
          // For very small numbers, show enough decimals
          if (Math.abs(price) < 0.0001) {
            return price.toFixed(8);
          }
          if (Math.abs(price) < 0.001) {
            return price.toFixed(6);
          }
          if (Math.abs(price) < 0.01) {
            return price.toFixed(5);
          }
          if (Math.abs(price) < 0.1) {
            return price.toFixed(4);
          }
          if (Math.abs(price) < 1) {
            return price.toFixed(3);
          }
          return price.toFixed(2);
        },
        minMove: 1e-8,
      },
    });

    // Get min and max prices from historical data to set scale
    // const prices = historicalData.map((d) => [d.high, d.low]).flat();
    chart.priceScale('right').applyOptions({
      autoScale: true,
      mode: 0,
      ticksVisible: true,
      entireTextOnly: false,
      // Set smaller top margin to utilize more vertical space
      scaleMargins: {
        top: 0.1,
        bottom: 0.2,
      },
    });

    // Volume series (histogram) on separate scale
    const volumeSeries = chart.addHistogramSeries({
      priceScaleId: 'volume',
      priceFormat: {
        type: 'volume',
      },
      color: '#26a69a',
    });

    // Create a separate scale on right for volume with correct margins
    chart.priceScale('volume').applyOptions({
      borderVisible: false,
      scaleMargins: {
        top: 0.8,
        bottom: 0,
      },
      visible: true,
    });

    // Balance series (line) on separate scale
    const balanceSeries = chart.addLineSeries({
      title: 'Suspect Wallet Token Balance',
      priceScaleId: 'balance',
      color: '#3B82F6', // Blue color for balance line
      lineWidth: 2,
      priceFormat: {
        type: 'custom',
        formatter: (price: number) => {
          // Format large balance numbers in a readable way
          if (price >= 1e12) {
            return (price / 1e12).toFixed(2) + 'T';
          }
          if (price >= 1e9) {
            return (price / 1e9).toFixed(2) + 'B';
          }
          if (price >= 1e6) {
            return (price / 1e6).toFixed(2) + 'M';
          }
          return price.toFixed(0);
        },
      },
    });

    const solBalanceSeries = chart.addLineSeries({
      title: 'Suspect Wallet SOL Balance',
      priceScaleId: 'sol_balance',
      color: '#A855F7', // Purple color for balance line
      lineWidth: 2,
      priceFormat: {
        type: 'custom',
        formatter: (price: number) => {
          // Format large balance numbers in a readable way
          if (price >= 1e12) {
            return (price / 1e12).toFixed(2) + 'T';
          }
          if (price >= 1e9) {
            return (price / 1e9).toFixed(2) + 'B';
          }
          if (price >= 1e6) {
            return (price / 1e6).toFixed(2) + 'M';
          }
          return price.toFixed(0);
        },
      },
    });

    // Create a separate scale on left for balance
    chart.priceScale('balance').applyOptions({
      borderVisible: true,
      borderColor: '#3B82F6',
      scaleMargins: {
        top: 0.1,
        bottom: 0.2,
      },
      visible: true,
    });

    // Subscribe to crosshair move for the tooltip
    chart.subscribeCrosshairMove((param) => {
      if (!param.time || !param.point) {
        setTooltipContent((prev) => ({ ...prev, visible: false }));
        return;
      }

      const rawData = param.seriesData.get(candlestickSeries);
      if (!rawData) {
        setTooltipContent((prev) => ({ ...prev, visible: false }));
        return;
      }

      const data = rawData as BarData;
      const localTime = (param.time as UTCTimestamp) * 1000;
      const date = new Date(localTime);
      // Format date as yyyy/mm/dd hh:mm
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const formattedDateTime = `${year}/${month}/${day} ${hours}:${minutes}`;

      setTooltipContent({
        time: formattedDateTime,
        open: data.open || 0,
        high: data.high || 0,
        low: data.low || 0,
        close: data.close || 0,
        volume: (param.seriesData.get(volumeSeries) as HistogramData)?.value || 0,
        visible: true,
      });
    });

    // Subscribe to long press events
    let longPressTimeout: NodeJS.Timeout;
    let isLongPress = false;

    const handleMouseDown = () => {
      longPressTimeout = setTimeout(() => {
        isLongPress = true;
        setTooltipContent((prev) => ({ ...prev, visible: true }));
      }, 300); // 300ms for long press
    };

    const handleMouseUp = () => {
      clearTimeout(longPressTimeout);
      if (!isLongPress) {
        // Keep tooltip visible on mouse up, don't hide it
      }
      isLongPress = false;
    };

    const handleMouseLeave = () => {
      clearTimeout(longPressTimeout);
      isLongPress = false;
      // Don't hide tooltip on mouse leave
    };

    chartContainerRef.current.addEventListener('mousedown', handleMouseDown);
    chartContainerRef.current.addEventListener('mouseup', handleMouseUp);
    chartContainerRef.current.addEventListener('mouseleave', handleMouseLeave);
    chartContainerRef.current.addEventListener('touchstart', handleMouseDown);
    chartContainerRef.current.addEventListener('touchend', handleMouseUp);

    chartRef.current = chart;
    candlestickSeriesRef.current = candlestickSeries;
    volumeSeriesRef.current = volumeSeries;
    balanceSeriesRef.current = balanceSeries;
    solBalanceSeriesRef.current = solBalanceSeries;
    // Subscribe to time range changes to update price lines visibility and load historical data
    chart.timeScale().subscribeVisibleTimeRangeChange((timeRange) => {
      // This ensures price lines remain visible when zooming or panning
      // Force chart redraw to ensure price lines remain visible
      if (chartRef.current) {
        // Apply a minimal resize to trigger a redraw
        const currentWidth = chartRef.current.options().width;
        chartRef.current.resize(currentWidth as number, chartRef.current.options().height as number);
      }

      // Check if user has scrolled to the left edge of the chart
      if (timeRange && timeRange.from) {
        // Only proceed if we have an oldest candle time and we're not already loading data
        if (oldestCandleTimeRef.current && !isLoadingMoreDataRef.current) {
          const visibleRangeStart = timeRange.from as number;

          // Simpler approach: if the user has scrolled to or beyond the oldest data point
          const isNearOldestData = visibleRangeStart <= oldestCandleTimeRef.current + 60 * 60;

          // If we're near the oldest data and not already in the loading state
          if (isNearOldestData) {
            console.log('Loading more historical data!');
            loadMoreHistoricalData();
          }
        }
      }
    });

    // Initial resize to fit container
    handleResize();

    const chartContainerRefCurrent = chartContainerRef.current;
    return () => {
      // Disconnect the ResizeObserver when component unmounts
      resizeObserver.disconnect();

      if (chartContainerRefCurrent) {
        chartContainerRefCurrent.removeEventListener('mousedown', handleMouseDown);
        chartContainerRefCurrent.removeEventListener('mouseup', handleMouseUp);
        chartContainerRefCurrent.removeEventListener('mouseleave', handleMouseLeave);
        chartContainerRefCurrent.removeEventListener('touchstart', handleMouseDown);
        chartContainerRefCurrent.removeEventListener('touchend', handleMouseUp);
      }
      chart.remove();
      chartRef.current = null;
      candlestickSeriesRef.current = null;
      volumeSeriesRef.current = null;
      balanceSeriesRef.current = null;
    };
  }, [tokenAddress, chainId, inApp, buySignalTime]);

  // Fetch initial data
  useEffect(() => {
    const fetchInitialData = async () => {
      if (!tokenAddress || !chainId) return;

      try {
        if (
          !candlestickSeriesRef.current || !volumeSeriesRef.current || !balanceSeriesRef.current || !chartRef.current
        ) {
          console.error('Chart series not initialized');
          return;
        }

        const data = await fetchChartData(tokenAddress, chainId, selectedTimeFrame, undefined, 1000);

        if (data.length > 0) {
          // Convert data to proper format
          const formattedData = data.map((d: ChartData) => ({
            ...d,
            time: Number(d.time) as UTCTimestamp,
          }));

          historicalDataRef.current = formattedData;
          oldestCandleTimeRef.current = formattedData[0].time;
          candlestickSeriesRef.current?.setData(formattedData);

          const volumeData = formattedData.map((d: ChartData) => ({
            time: d.time,
            value: d.volume,
            color: d.close >= d.open ? '#00B38C' : '#db2777', // Tailwind pink-600
          }));
          volumeSeriesRef.current?.setData(volumeData);
          balanceSeriesRef.current?.setData(processedBalanceData);
          solBalanceSeriesRef.current?.setData(processedSolBalanceData);

          // Set the last bar for real-time updates
          lastBarRef.current = formattedData[formattedData.length - 1];

          // Update price in context with the latest price
          const last = formattedData[formattedData.length - 1];
          updateTokenPrice(tokenAddress, last.close);
        } else {
          historicalDataRef.current = [];
          candlestickSeriesRef.current?.setData([]);
          volumeSeriesRef.current?.setData([]);
          balanceSeriesRef.current?.setData([]);
          solBalanceSeriesRef.current?.setData([]);
        }
      } catch (error) {
        console.error('Failed to fetch initial data:', error);
      }
    };

    fetchInitialData();
  }, [
    tokenAddress,
    chainId,
    selectedTimeFrame,
    updateTokenPrice,
    processedBalanceData,
    processedSolBalanceData,
  ]);

  return (
    <div className="relative h-full">
      {/* Timeframe buttons at top-left */}
      <div className="absolute left-2 top-2 z-10 flex flex-wrap gap-1 md:gap-2">
        {timeFrames.map((tf) => (
          <button
            key={tf}
            onClick={() => setSelectedTimeFrame(tf)}
            className={`rounded px-1.5 py-0.5 text-xs md:px-2 md:py-1 ${
              selectedTimeFrame === tf ? 'bg-zinc-700 text-white' : 'bg-zinc-800 text-zinc-400 hover:bg-zinc-700'
            }`}
          >
            {tf}
          </button>
        ))}
      </div>

      {/* QuotationWebSocket component */}
      <QuotationWebSocket {...quotationWebSocketProps} />

      {/* Tooltip element - fixed position at top-left */}
      <div
        id="chart-tooltip"
        className="absolute left-2 top-10 z-50 rounded border border-zinc-700 bg-zinc-900/90 p-2 shadow-lg backdrop-blur-sm"
        style={{ pointerEvents: 'none', display: tooltipContent.visible ? 'block' : 'none' }}
      >
        <div className="space-y-1 text-xs font-medium">
          <div className="text-zinc-400">Time: {tooltipContent.time}</div>
          <div className="flex items-center gap-2">
            <span className="text-zinc-400">Open:</span>
            <span className="text-white">{formatPriceWithDecimals(tooltipContent.open)}</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-zinc-400">High:</span>
            <span className="text-white">{formatPriceWithDecimals(tooltipContent.high)}</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-zinc-400">Low:</span>
            <span className="text-white">{formatPriceWithDecimals(tooltipContent.low)}</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-zinc-400">Close:</span>
            <span className="text-white">{formatPriceWithDecimals(tooltipContent.close)}</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-zinc-400">Vol:</span>
            <span className="text-white">
              {formatNumber(tooltipContent.volume, { notation: 'compact', maximumFractionDigits: 2 })}
            </span>
          </div>
        </div>
      </div>

      {/* Chart container */}
      <div ref={chartContainerRef} data-testid="trading-view-chart" className="h-full w-full" />

      {/* Timezone indicator at bottom right */}
      <div className="absolute bottom-[-10px] right-2 z-10 rounded bg-zinc-800 px-2 py-0.5 text-xs text-zinc-400">
        {(() => {
          const date = new Date();
          const offset = -date.getTimezoneOffset() / 60;
          return (
            <>
              <div>{Intl.DateTimeFormat().resolvedOptions().timeZone.split('/').pop()}</div>
              <div>{`UTC${offset >= 0 ? '+' : ''}${offset}`}</div>
            </>
          );
        })()}
      </div>
    </div>
  );
}
